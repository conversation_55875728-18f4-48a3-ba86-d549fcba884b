# 哲学知识图谱平台

这是一个基于AI的交互式哲学知识图谱学习平台，使用React、D3.js和OpenAI技术构建，帮助用户可视化探索哲学概念及其关系。

## 🌟 主要特性

- **AI驱动的知识图谱生成**：使用302ai API服务生成高质量的哲学概念图谱
- **交互式可视化**：基于D3.js的力导向图布局，支持缩放、拖拽和节点交互
- **深度概念解释**：采用前辈引导式思维框架，提供温度感的概念解释
- **中文界面**：完全本土化的用户体验
- **响应式设计**：适配桌面和移动设备

## 🏗️ 技术架构

### 前端技术栈
- **React 18** + TypeScript
- **Tailwind CSS** + shadcn/ui 组件系统
- **D3.js** 数据可视化
- **TanStack Query** 状态管理
- **Wouter** 轻量级路由

### 后端技术栈
- **Node.js** + Express
- **TypeScript** ES模块
- **OpenAI GPT-4o** AI集成（通过302ai服务）
- **PostgreSQL** + Drizzle ORM
- **Zod** 数据验证

### 核心功能
- 🎯 智能哲学概念搜索和建议
- 📊 6阶段AI生成进度可视化
- 🎨 美观的知识图谱渲染
- 📱 移动端友好的侧边栏交互
- 🔍 概念详情展示（哲学家、著作、相关概念）

## 🚀 快速开始

1. **安装依赖**
   ```bash
   npm install
   ```

2. **设置环境变量**
   ```bash
   # 创建 .env 文件
   OPENAI_API_KEY=your_302ai_api_key
   OPENAI_BASE_URL=https://api.302.ai/v1
   ```

3. **启动开发服务器**
   ```bash
   npm run dev
   ```

4. **访问应用**
   打开 http://localhost:5000

## 📱 使用方法

1. **搜索哲学概念**：在搜索框中输入感兴趣的哲学主题
2. **生成知识图谱**：AI将在6个阶段中构建概念关系网络
3. **探索概念**：点击图谱中的节点查看详细解释
4. **深度学习**：通过侧边栏了解相关哲学家、著作和概念

## 🎨 设计理念

采用Observable/Roam Research启发的设计风格：
- **色彩方案**：深蓝海军色(#2C3E50)、智慧紫(#8E44AD)、启发橙(#E67E22)
- **字体选择**：Crimson Text(标题) + Source Sans Pro(正文) + Fira Code(代码)
- **交互设计**：温和的悬停效果和平滑的过渡动画

## 🔧 开发指南

### 项目结构
```
├── client/          # React前端应用
│   ├── src/
│   │   ├── components/  # UI组件
│   │   ├── pages/       # 页面组件
│   │   └── hooks/       # 自定义钩子
├── server/          # Express后端服务
│   ├── services/    # 业务逻辑服务
│   └── routes.ts    # API路由定义
├── shared/          # 共享类型定义
└── README.md
```

### 主要组件
- `KnowledgeGraph`: D3.js可视化组件
- `ConceptSidebar`: 概念详情侧边栏
- `SearchInterface`: 搜索和建议界面

### AI服务集成
使用302ai提供的OpenAI兼容API，专门为中文哲学概念优化了提示词。

## 📄 许可证

MIT License - 详见 LICENSE 文件

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

---

**构建时间**: 2025年7月
**技术支持**: Replit平台 + 302ai服务