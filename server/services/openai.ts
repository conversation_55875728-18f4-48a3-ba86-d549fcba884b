import OpenAI from "openai";
import { PhilosophyGraph, GraphNode, GraphLink, PhilosopherInfo } from "@shared/schema";

// the newest OpenAI model is "gpt-4o" which was released May 13, 2024. do not change this unless explicitly requested by the user
const openai = new OpenAI({ 
  apiKey: process.env.OPENAI_API_KEY || process.env.OPENAI_API_KEY_ENV_VAR || "default_key",
  baseURL: process.env.OPENAI_BASE_URL || "https://api.302.ai/v1"
});

export async function generatePhilosophyGraph(topic: string): Promise<PhilosophyGraph> {
  try {
    const prompt = `作为一位在哲学领域深耕多年的前辈，我需要为刚入门的学习者创建关于"${topic}"的知识图谱。

=== 我的身份与使命 ===
我是一位既有俯瞰全局视野，又保持对新人困境共情的哲学引路人。我的核心信念是：真正的智慧不在于知道一切，而在于知道什么最重要。我要点亮路径，而非倾倒知识。

=== 生成指导原则 ===
- 实用性 > 全面性：专注于能立即理解和应用的核心洞察
- 底层逻辑 > 表面现象：揭示概念的本质关系和内在逻辑
- 连接 > 孤立：展现概念间的关系网络，构建完整的认知地图
- 温度感：像咖啡馆里推心置腹的对话，用故事让概念鲜活

请用JSON格式返回包含以下结构的对象：
{
  "topic": "${topic}",
  "nodes": [
    {
      "id": "唯一标识符",
      "name": "概念名称",
      "definition": "这里是核心！按以下结构深度解释：\\n\\n## 💡 核心洞察\\n用一两句话抓住概念的本质，像是你多年经验的总结\\n\\n## 🏗️ 承重墙（核心要素）\\n列出2-3个移除就会坍塌的关键要素，解释为什么它们不可或缺\\n\\n## 🎭 真实场景\\n用具体的例子或故事说明这个概念如何在现实中发挥作用\\n\\n## 🤝 关系网络\\n简要说明它与其他重要概念的关系，帮助建立整体理解\\n\\n## ⚡ 实践智慧\\n提供一个能立即应用的洞察或思考角度",
      "category": "core|related|principle",
      "keyPhilosophers": [
        {
          "name": "哲学家姓名",
          "description": "用温暖的语言描述他们的独特贡献，重点在洞察而非履历",
          "initials": "姓名缩写"
        }
      ],
      "keyWorks": ["经典著作，重点选择对理解此概念最有帮助的"],
      "relatedConcepts": ["直接相关的核心概念"]
    }
  ],
  "links": [
    {
      "source": "源节点ID",
      "target": "目标节点ID", 
      "relationship": "用生动的语言描述关系本质",
      "strength": 0.3到1.0的关系强度
    }
  ]
}

=== 特别要求 ===
1. 创建5-8个相关概念节点，构建完整的认知地图
2. 每个解释都要有温度感和实用性，像前辈的亲身指导
3. 节点分类要体现认知层次：core(地基概念)、related(拓展理解)、principle(行动指南)
4. 关系描述要揭示深层逻辑，而非表面联系
5. 哲学家介绍重点在思想贡献，而非生平事迹
6. 所有内容用中文，语言要有温度和洞察力

请确保返回有效的JSON格式，definition字段使用上述markdown结构增强可读性。`;

    const response = await openai.chat.completions.create({
      model: "gpt-4o",
      messages: [
        {
          role: "system",
          content: "你是一位富有智慧的哲学前辈，专门为初学者创建详细的知识图谱。请务必返回有效的JSON格式。"
        },
        {
          role: "user",
          content: prompt
        }
      ],
      response_format: { type: "json_object" },
      temperature: 0.7,
      max_tokens: 3000
    });

    const result = JSON.parse(response.choices[0].message.content || "{}");
    
    // Validate and ensure proper structure
    if (!result.topic || !result.nodes || !result.links) {
      throw new Error("Invalid response structure from OpenAI");
    }

    // Add unique IDs if missing and validate structure
    result.nodes = result.nodes.map((node: any, index: number) => ({
      id: node.id || `node-${index}`,
      name: node.name || `Concept ${index}`,
      definition: node.definition || "No definition provided",
      category: node.category || "related",
      keyPhilosophers: Array.isArray(node.keyPhilosophers) ? node.keyPhilosophers : [],
      keyWorks: Array.isArray(node.keyWorks) ? node.keyWorks : [],
      relatedConcepts: Array.isArray(node.relatedConcepts) ? node.relatedConcepts : []
    }));

    result.links = result.links.map((link: any) => ({
      source: link.source,
      target: link.target,
      relationship: link.relationship || "related to",
      strength: Math.max(0.3, Math.min(1.0, link.strength || 0.5))
    }));

    return result as PhilosophyGraph;

  } catch (error) {
    console.error("Error generating philosophy graph:", error);
    
    // Fallback: return a basic graph structure
    return createFallbackGraph(topic);
  }
}

function createFallbackGraph(topic: string): PhilosophyGraph {
  return {
    topic,
    nodes: [
      {
        id: "main",
        name: topic,
        definition: `## 💡 核心洞察\n${topic}是哲学思考中的一个重要概念，涉及对人类存在和行为的深层思考。\n\n## 🏗️ 承重墙（核心要素）\n- **理性思辨**：通过逻辑推理探索概念本质\n- **历史传承**：建立在前人思想基础之上\n- **现实意义**：与我们的日常生活息息相关\n\n## 🎭 真实场景\n这个概念在我们面临重要抉择时发挥作用，帮助我们更深入地理解自己和世界的关系。\n\n## 🤝 关系网络\n与伦理学、形而上学、认识论等哲学分支紧密相连，共同构成哲学思想的完整图景。\n\n## ⚡ 实践智慧\n在思考这个概念时，试着问自己：这对我的生活有什么启发？`,
        category: "core",
        keyPhilosophers: [
          { name: "历代哲学家", description: "为这一概念的发展做出了重要贡献", initials: "历" }
        ],
        keyWorks: [`${topic}相关研究`],
        relatedConcepts: ["哲学", "伦理学", "形而上学"]
      },
      {
        id: "related-1",
        name: "相关概念",
        definition: `## 💡 核心洞察\n这是与${topic}密切相关的一个概念，有助于我们从不同角度理解主题。\n\n## 🏗️ 承重墙（核心要素）\n- **补充视角**：提供新的思考维度\n- **概念联系**：与主题形成有机整体\n\n## 🎭 真实场景\n在深入理解${topic}时，这个概念能帮助我们看到更全面的图景。\n\n## 🤝 关系网络\n作为${topic}的重要补充，它们共同构建了完整的理解框架。\n\n## ⚡ 实践智慧\n理解这个相关概念，能让我们对${topic}有更深层的把握。`,
        category: "related",
        keyPhilosophers: [
          { name: "当代思想家", description: "从现代视角解读了这一概念", initials: "当" }
        ],
        keyWorks: [`现代${topic}观点`],
        relatedConcepts: [topic]
      }
    ],
    links: [
      {
        source: "main",
        target: "related-1",
        relationship: "相互补充，共同构建完整理解",
        strength: 0.8
      }
    ]
  };
}

export async function getSuggestions(query: string): Promise<string[]> {
  try {
    const prompt = `Given the philosophical search query "${query}", suggest 5 related philosophical topics or concepts that a user might be interested in exploring. 

Return a JSON array of strings, for example:
["Existentialism", "Moral Philosophy", "Free Will", "Consciousness", "Ethics"]

Focus on major philosophical topics, movements, concepts, or questions. Keep suggestions concise and academically relevant.`;

    const response = await openai.chat.completions.create({
      model: "gpt-4o",
      messages: [
        {
          role: "system", 
          content: "You are a philosophy expert. Provide relevant philosophical topic suggestions as a JSON array."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      response_format: { type: "json_object" },
      temperature: 0.5,
      max_tokens: 200
    });

    const result = JSON.parse(response.choices[0].message.content || "{}");
    return Array.isArray(result.suggestions) ? result.suggestions : [];

  } catch (error) {
    console.error("Error getting suggestions:", error);
    return ["Ethics", "Existentialism", "Free Will", "Consciousness", "Philosophy of Mind"];
  }
}
