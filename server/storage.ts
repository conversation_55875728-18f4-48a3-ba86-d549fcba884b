import { 
  users, 
  philosophyConcepts, 
  knowledgeGraphs,
  type User, 
  type InsertUser,
  type PhilosophyConcept,
  type InsertPhilosophyConcept,
  type KnowledgeGraph,
  type InsertKnowledgeGraph,
  type PhilosopherInfo
} from "@shared/schema";

export interface IStorage {
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  
  getPhilosophyConcept(id: number): Promise<PhilosophyConcept | undefined>;
  getPhilosophyConceptByName(name: string): Promise<PhilosophyConcept | undefined>;
  searchPhilosophyConcepts(query: string): Promise<PhilosophyConcept[]>;
  createPhilosophyConcept(concept: InsertPhilosophyConcept): Promise<PhilosophyConcept>;
  getAllPhilosophyConcepts(): Promise<PhilosophyConcept[]>;
  
  getKnowledgeGraph(id: number): Promise<KnowledgeGraph | undefined>;
  getKnowledgeGraphByTopic(topic: string): Promise<KnowledgeGraph | undefined>;
  createKnowledgeGraph(graph: InsertKnowledgeGraph): Promise<KnowledgeGraph>;
}

export class MemStorage implements IStorage {
  private users: Map<number, User>;
  private philosophyConcepts: Map<number, PhilosophyConcept>;
  private knowledgeGraphs: Map<number, KnowledgeGraph>;
  private currentUserId: number;
  private currentConceptId: number;
  private currentGraphId: number;

  constructor() {
    this.users = new Map();
    this.philosophyConcepts = new Map();
    this.knowledgeGraphs = new Map();
    this.currentUserId = 1;
    this.currentConceptId = 1;
    this.currentGraphId = 1;
    
    // Initialize with some core philosophy concepts
    this.initializePhilosophyConcepts();
  }

  private initializePhilosophyConcepts() {
    const concepts: InsertPhilosophyConcept[] = [
      {
        name: "Existentialism",
        definition: "A philosophical movement that emphasizes individual existence, freedom, and choice. Existentialists believe that humans create their own meaning and essence through their actions and decisions.",
        category: "core",
        keyPhilosophers: [
          { name: "Jean-Paul Sartre", description: "Existence precedes essence", initials: "JS" },
          { name: "Albert Camus", description: "Absurdist philosophy", initials: "AC" },
          { name: "Søren Kierkegaard", description: "Father of existentialism", initials: "SK" }
        ],
        keyWorks: ["Being and Nothingness - Sartre", "The Stranger - Camus", "Fear and Trembling - Kierkegaard"],
        relatedConcepts: ["Authenticity", "Freedom", "Responsibility", "Bad Faith"]
      },
      {
        name: "Ethics",
        definition: "The branch of philosophy that deals with moral principles and values, determining what is right and wrong in human conduct.",
        category: "core",
        keyPhilosophers: [
          { name: "Aristotle", description: "Virtue ethics", initials: "AR" },
          { name: "Immanuel Kant", description: "Categorical imperative", initials: "IK" },
          { name: "John Stuart Mill", description: "Utilitarianism", initials: "JS" }
        ],
        keyWorks: ["Nicomachean Ethics - Aristotle", "Groundwork for the Metaphysics of Morals - Kant", "Utilitarianism - Mill"],
        relatedConcepts: ["Virtue", "Duty", "Consequentialism", "Deontology"]
      },
      {
        name: "Free Will",
        definition: "The ability to make choices that are genuinely one's own, unconstrained by prior causes or divine decree.",
        category: "core",
        keyPhilosophers: [
          { name: "William James", description: "Pragmatist approach to free will", initials: "WJ" },
          { name: "Daniel Dennett", description: "Compatibilist view", initials: "DD" },
          { name: "Robert Kane", description: "Libertarian free will", initials: "RK" }
        ],
        keyWorks: ["Elbow Room - Dennett", "The Significance of Free Will - Kane", "The Will to Believe - James"],
        relatedConcepts: ["Determinism", "Moral Responsibility", "Choice", "Agency"]
      },
      {
        name: "Consciousness",
        definition: "The state of being aware and having subjective experiences, including thoughts, feelings, and perceptions.",
        category: "core",
        keyPhilosophers: [
          { name: "David Chalmers", description: "Hard problem of consciousness", initials: "DC" },
          { name: "Thomas Nagel", description: "What is it like to be a bat?", initials: "TN" },
          { name: "John Searle", description: "Chinese room argument", initials: "JS" }
        ],
        keyWorks: ["The Conscious Mind - Chalmers", "What Is It Like to Be a Bat? - Nagel", "Minds, Brains, and Science - Searle"],
        relatedConcepts: ["Qualia", "Mind-Body Problem", "Phenomenology", "Intentionality"]
      }
    ];

    concepts.forEach(concept => this.createPhilosophyConcept(concept));
  }

  async getUser(id: number): Promise<User | undefined> {
    return this.users.get(id);
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(
      (user) => user.username === username,
    );
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const id = this.currentUserId++;
    const user: User = { ...insertUser, id };
    this.users.set(id, user);
    return user;
  }

  async getPhilosophyConcept(id: number): Promise<PhilosophyConcept | undefined> {
    return this.philosophyConcepts.get(id);
  }

  async getPhilosophyConceptByName(name: string): Promise<PhilosophyConcept | undefined> {
    return Array.from(this.philosophyConcepts.values()).find(
      (concept) => concept.name.toLowerCase() === name.toLowerCase()
    );
  }

  async searchPhilosophyConcepts(query: string): Promise<PhilosophyConcept[]> {
    const lowercaseQuery = query.toLowerCase();
    return Array.from(this.philosophyConcepts.values()).filter(
      (concept) => 
        concept.name.toLowerCase().includes(lowercaseQuery) ||
        concept.definition.toLowerCase().includes(lowercaseQuery) ||
        concept.category.toLowerCase().includes(lowercaseQuery)
    ).slice(0, 5); // Limit to 5 suggestions
  }

  async createPhilosophyConcept(insertConcept: InsertPhilosophyConcept): Promise<PhilosophyConcept> {
    const id = this.currentConceptId++;
    const concept: PhilosophyConcept = { 
      ...insertConcept, 
      id,
      createdAt: new Date()
    };
    this.philosophyConcepts.set(id, concept);
    return concept;
  }

  async getAllPhilosophyConcepts(): Promise<PhilosophyConcept[]> {
    return Array.from(this.philosophyConcepts.values());
  }

  async getKnowledgeGraph(id: number): Promise<KnowledgeGraph | undefined> {
    return this.knowledgeGraphs.get(id);
  }

  async getKnowledgeGraphByTopic(topic: string): Promise<KnowledgeGraph | undefined> {
    return Array.from(this.knowledgeGraphs.values()).find(
      (graph) => graph.topic.toLowerCase() === topic.toLowerCase()
    );
  }

  async createKnowledgeGraph(insertGraph: InsertKnowledgeGraph): Promise<KnowledgeGraph> {
    const id = this.currentGraphId++;
    const graph: KnowledgeGraph = {
      ...insertGraph,
      id,
      createdAt: new Date()
    };
    this.knowledgeGraphs.set(id, graph);
    return graph;
  }
}

export const storage = new MemStorage();
