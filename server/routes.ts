import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { generatePhilosophyGraph, getSuggestions } from "./services/openai";
import { z } from "zod";

const generateGraphSchema = z.object({
  topic: z.string().min(1).max(200)
});

const searchSchema = z.object({
  query: z.string().min(1).max(100)
});

export async function registerRoutes(app: Express): Promise<Server> {
  
  // Generate philosophy knowledge graph
  app.post("/api/philosophy/generate", async (req, res) => {
    try {
      const { topic } = generateGraphSchema.parse(req.body);
      
      // Check if graph already exists
      let existingGraph = await storage.getKnowledgeGraphByTopic(topic);
      
      if (existingGraph) {
        return res.json(existingGraph);
      }
      
      // Generate new graph using OpenAI
      const philosophyGraph = await generatePhilosophyGraph(topic);
      
      // Store the generated graph
      const savedGraph = await storage.createKnowledgeGraph({
        topic: philosophyGraph.topic,
        nodes: philosophyGraph.nodes,
        links: philosophyGraph.links
      });
      
      res.json(savedGraph);
      
    } catch (error) {
      console.error("Error generating philosophy graph:", error);
      if (error instanceof z.ZodError) {
        return res.status(400).json({ 
          message: "Invalid request data", 
          errors: error.errors 
        });
      }
      res.status(500).json({ 
        message: "Failed to generate philosophy graph. Please try again." 
      });
    }
  });

  // Get philosophy concept suggestions
  app.get("/api/philosophy/suggestions", async (req, res) => {
    try {
      const query = req.query.q as string;
      
      if (!query || query.length < 1) {
        return res.json([]);
      }
      
      // First, search local concepts
      const localConcepts = await storage.searchPhilosophyConcepts(query);
      
      // If we have enough local results, return them
      if (localConcepts.length >= 3) {
        const suggestions = localConcepts.map(concept => ({
          name: concept.name,
          description: concept.definition.substring(0, 100) + "..."
        }));
        return res.json(suggestions);
      }
      
      // Otherwise, get AI suggestions
      const aiSuggestions = await getSuggestions(query);
      
      // Combine local and AI suggestions
      const combinedSuggestions = [
        ...localConcepts.map(concept => ({
          name: concept.name,
          description: concept.definition.substring(0, 100) + "..."
        })),
        ...aiSuggestions.slice(0, 5 - localConcepts.length).map(name => ({
          name,
          description: `Philosophical concept or movement related to ${name.toLowerCase()}`
        }))
      ];
      
      res.json(combinedSuggestions);
      
    } catch (error) {
      console.error("Error getting suggestions:", error);
      res.status(500).json({ 
        message: "Failed to get suggestions" 
      });
    }
  });

  // Get specific philosophy concept
  app.get("/api/philosophy/concept/:name", async (req, res) => {
    try {
      const conceptName = req.params.name;
      const concept = await storage.getPhilosophyConceptByName(conceptName);
      
      if (!concept) {
        return res.status(404).json({ 
          message: "Philosophy concept not found" 
        });
      }
      
      res.json(concept);
      
    } catch (error) {
      console.error("Error getting philosophy concept:", error);
      res.status(500).json({ 
        message: "Failed to get philosophy concept" 
      });
    }
  });

  // Get all philosophy concepts
  app.get("/api/philosophy/concepts", async (req, res) => {
    try {
      const concepts = await storage.getAllPhilosophyConcepts();
      res.json(concepts);
    } catch (error) {
      console.error("Error getting philosophy concepts:", error);
      res.status(500).json({ 
        message: "Failed to get philosophy concepts" 
      });
    }
  });

  // Get knowledge graph by topic
  app.get("/api/philosophy/graph/:topic", async (req, res) => {
    try {
      const topic = req.params.topic;
      const graph = await storage.getKnowledgeGraphByTopic(topic);
      
      if (!graph) {
        return res.status(404).json({ 
          message: "Knowledge graph not found for this topic" 
        });
      }
      
      res.json(graph);
      
    } catch (error) {
      console.error("Error getting knowledge graph:", error);
      res.status(500).json({ 
        message: "Failed to get knowledge graph" 
      });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
