import { pgTable, text, serial, integer, jsonb, timestamp } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  username: text("username").notNull().unique(),
  password: text("password").notNull(),
});

export const philosophyConcepts = pgTable("philosophy_concepts", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  definition: text("definition").notNull(),
  category: text("category").notNull(),
  keyPhilosophers: jsonb("key_philosophers").$type<PhilosopherInfo[]>().notNull(),
  keyWorks: jsonb("key_works").$type<string[]>().notNull(),
  relatedConcepts: jsonb("related_concepts").$type<string[]>().notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

export const knowledgeGraphs = pgTable("knowledge_graphs", {
  id: serial("id").primaryKey(),
  topic: text("topic").notNull(),
  nodes: jsonb("nodes").$type<GraphNode[]>().notNull(),
  links: jsonb("links").$type<GraphLink[]>().notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

// Schema types
export interface PhilosopherInfo {
  name: string;
  description: string;
  initials: string;
}

export interface GraphNode {
  id: string;
  name: string;
  definition: string;
  category: 'core' | 'related' | 'principle';
  keyPhilosophers: PhilosopherInfo[];
  keyWorks: string[];
  relatedConcepts: string[];
  x?: number;
  y?: number;
}

export interface GraphLink {
  source: string;
  target: string;
  relationship: string;
  strength: number;
}

export interface PhilosophyGraph {
  topic: string;
  nodes: GraphNode[];
  links: GraphLink[];
}

// Insert schemas
export const insertPhilosophyConceptSchema = createInsertSchema(philosophyConcepts).omit({
  id: true,
  createdAt: true,
});

export const insertKnowledgeGraphSchema = createInsertSchema(knowledgeGraphs).omit({
  id: true,
  createdAt: true,
});

export const insertUserSchema = createInsertSchema(users).pick({
  username: true,
  password: true,
});

// Types
export type InsertPhilosophyConcept = z.infer<typeof insertPhilosophyConceptSchema>;
export type PhilosophyConcept = typeof philosophyConcepts.$inferSelect;
export type InsertKnowledgeGraph = z.infer<typeof insertKnowledgeGraphSchema>;
export type KnowledgeGraph = typeof knowledgeGraphs.$inferSelect;
export type InsertUser = z.infer<typeof insertUserSchema>;
export type User = typeof users.$inferSelect;
