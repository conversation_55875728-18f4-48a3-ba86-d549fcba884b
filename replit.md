# Philosophy Knowledge Graph Application

## Overview

This is a full-stack TypeScript application that generates interactive knowledge graphs for philosophical topics using AI. The system allows users to search for philosophical concepts and generates dynamic, visual knowledge graphs showing relationships between concepts, philosophers, and key works.

## System Architecture

### Frontend Architecture
- **Framework**: React 18 with TypeScript
- **Styling**: Tailwind CSS with custom CSS variables for theming
- **UI Components**: Radix UI primitives with shadcn/ui component system
- **State Management**: TanStack Query (React Query) for server state
- **Routing**: Wouter for lightweight client-side routing
- **Data Visualization**: D3.js for interactive knowledge graph rendering
- **Build Tool**: Vite with React plugin

### Backend Architecture
- **Runtime**: Node.js with Express.js
- **Language**: TypeScript with ES modules
- **API Design**: RESTful endpoints with Zod validation
- **Database**: PostgreSQL with Drizzle ORM
- **AI Integration**: OpenAI GPT-4o for philosophy graph generation
- **Session Management**: Connect-pg-simple for PostgreSQL session storage

### Data Storage Solutions
- **Primary Database**: PostgreSQL (configured for Neon serverless)
- **ORM**: Drizzle with type-safe schema definitions
- **Fallback Storage**: In-memory storage implementation for development
- **Schema**: Philosophy concepts, knowledge graphs, and user data tables

## Key Components

### Database Schema
1. **Users Table**: Basic user authentication and management
2. **Philosophy Concepts Table**: Individual philosophical concepts with definitions, categories, philosophers, and relationships
3. **Knowledge Graphs Table**: Complete graph structures with nodes and links stored as JSONB

### AI Service Integration
- OpenAI GPT-4o integration for generating philosophical knowledge graphs
- Structured prompt engineering for consistent graph output
- JSON schema validation for AI responses
- Fallback and error handling for AI service failures

### Interactive Visualization
- D3.js force-directed graph layout for knowledge visualization
- Interactive node selection and exploration
- Zoom and pan functionality for large graphs
- Responsive design with mobile-optimized interactions

### Search and Discovery
- Real-time search suggestions for philosophical topics
- Quick-start topics for common philosophical areas
- Caching system for previously generated graphs

## Data Flow

1. **User Input**: User searches for a philosophical topic through the search interface
2. **Validation**: Frontend validates input and checks for existing graphs
3. **AI Generation**: If no cached graph exists, backend calls OpenAI to generate new knowledge graph
4. **Storage**: Generated graph is stored in PostgreSQL for future retrieval
5. **Visualization**: Frontend receives graph data and renders interactive D3.js visualization
6. **Interaction**: Users can click nodes to explore detailed concept information in sidebar

## External Dependencies

### Core Dependencies
- **@neondatabase/serverless**: PostgreSQL connection for Neon serverless database
- **drizzle-orm**: Type-safe database operations and schema management
- **openai**: Official OpenAI API client for GPT-4o integration
- **@tanstack/react-query**: Server state management and caching
- **d3**: Data visualization and graph layout algorithms
- **@radix-ui/**: Accessible UI primitive components
- **zod**: Runtime type validation and schema definition

### Development Tools
- **tsx**: TypeScript execution for development server
- **esbuild**: Fast bundling for production builds
- **@replit/vite-plugin-cartographer**: Replit-specific development tools

## Deployment Strategy

### Development Environment
- Vite development server with hot module replacement
- TSX for running TypeScript server code directly
- Replit integration with development banner and cartographer

### Production Build
1. **Frontend**: Vite builds React application to `dist/public`
2. **Backend**: ESBuild bundles server code to `dist/index.js`
3. **Database**: Drizzle migrations applied via `db:push` command
4. **Environment**: NODE_ENV=production with optimized builds

### Environment Configuration
- DATABASE_URL for PostgreSQL connection
- OPENAI_API_KEY for AI service integration
- Session secrets for secure authentication
- Automatic Replit environment detection

## Changelog
- July 07, 2025. 中文界面本地化和302ai API集成
  - 将整个用户界面翻译为中文（搜索、导航、加载、错误信息等）
  - 配置支持302ai API服务（https://api.302.ai/v1）
  - 修复CSS样式警告（@import顺序）
  - 快速开始主题改为中文哲学概念
  - 修复节点悬停抖动问题（将hover效果从JS移到CSS）
  - 添加知识图谱生成进度条
    * 显示AI处理的6个阶段（分析主题→识别概念→构建关系→生成节点→优化结构→完成）
    * 美化的进度界面，包含百分比和阶段描述
    * 平滑的进度动画和模糊背景效果
  - 重新设计概念解释系统
    * 将"定义"改为"解释"，提供更深度的内容
    * 采用前辈引导式思维框架（核心洞察、承重墙、真实场景、关系网络、实践智慧）
    * 支持markdown格式渲染，包含表情符号和结构化内容
    * AI生成内容更具温度感和实用性
- July 03, 2025. Initial setup

## User Preferences

Preferred communication style: Simple, everyday language.
Preferred language: Chinese (中文界面)
API Provider: 302ai (configured for compatibility)