import { useState, useRef, useEffect } from "react";
import { Search, BookOpen, Users, Target, Lightbulb } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { usePhilosophySuggestions } from "@/hooks/use-philosophy-graph";
import { SearchSuggestion } from "@/lib/types";

interface SearchInterfaceProps {
  onSearch: (topic: string) => void;
  isLoading: boolean;
}

export function SearchInterface({ onSearch, isLoading }: SearchInterfaceProps) {
  const [searchTopic, setSearchTopic] = useState("");
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [debouncedQuery, setDebouncedQuery] = useState("");
  const inputRef = useRef<HTMLInputElement>(null);
  
  // Debounce search query
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedQuery(searchTopic);
    }, 300);
    
    return () => clearTimeout(timer);
  }, [searchTopic]);

  const { data: suggestions = [] } = usePhilosophySuggestions(debouncedQuery);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchTopic.trim()) {
      onSearch(searchTopic.trim());
      setShowSuggestions(false);
    }
  };

  const handleSuggestionClick = (suggestion: SearchSuggestion) => {
    setSearchTopic(suggestion.name);
    onSearch(suggestion.name);
    setShowSuggestions(false);
  };

  const quickStartTopics = [
    { name: "存在主义", icon: Users },
    { name: "伦理学", icon: Target },
    { name: "意识", icon: Lightbulb },
    { name: "自由意志", icon: BookOpen }
  ];

  return (
    <div className="bg-surface-primary border-b border-gray-200">
      <div className="max-w-4xl mx-auto px-4 py-12">
        <div className="text-center mb-10">
          <h2 className="font-inter text-4xl font-semibold text-gray-900 mb-3 tracking-tight text-balance">
            探索哲学概念
          </h2>
          <p className="text-gray-600 font-inter text-lg leading-relaxed max-w-2xl mx-auto">
            输入任何哲学主题来生成交互式知识图谱，发现思想之间的深层联系
          </p>
        </div>

        <form onSubmit={handleSubmit} className="relative max-w-2xl mx-auto">
          <div className="relative group">
            <Input
              ref={inputRef}
              type="text"
              placeholder="输入哲学主题（例如：存在主义、自由意志、德性伦理学）"
              value={searchTopic}
              onChange={(e) => setSearchTopic(e.target.value)}
              onFocus={() => setShowSuggestions(true)}
              onBlur={() => setTimeout(() => setShowSuggestions(false), 200)}
              className="w-full px-6 py-4 text-lg font-inter bg-surface-primary border border-gray-200 rounded-xl focus:border-primary-purple focus:ring-4 focus:ring-primary-purple/10 pr-16 transition-all duration-200 shadow-soft hover:shadow-medium input-enhanced"
              disabled={isLoading}
            />
            <Button
              type="submit"
              disabled={!searchTopic.trim() || isLoading}
              className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-primary-purple text-white p-3 rounded-lg hover:bg-primary-purple/90 transition-all duration-200 shadow-soft hover:shadow-purple hover:-translate-y-0.5 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
            >
              <Search className="w-4 h-4" />
            </Button>
          </div>
          
          {/* Search Suggestions */}
          {showSuggestions && suggestions.length > 0 && (
            <div className="absolute top-full left-0 right-0 bg-surface-primary border border-gray-200 rounded-xl mt-2 shadow-large z-10 animate-scale-in overflow-hidden">
              {suggestions.map((suggestion, index) => (
                <div
                  key={index}
                  onClick={() => handleSuggestionClick(suggestion)}
                  className="px-4 py-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0 transition-colors duration-150"
                >
                  <div className="font-medium text-gray-900 font-inter">{suggestion.name}</div>
                  <div className="text-sm text-gray-600 font-inter">{suggestion.description}</div>
                </div>
              ))}
            </div>
          )}
        </form>

        {/* Quick Start Topics */}
        <div className="mt-8 text-center">
          <p className="text-sm text-gray-500 mb-4 font-inter">快速开始：</p>
          <div className="flex flex-wrap justify-center gap-3">
            {quickStartTopics.map((topic) => {
              const IconComponent = topic.icon;
              return (
                <Button
                  key={topic.name}
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setSearchTopic(topic.name);
                    onSearch(topic.name);
                  }}
                  disabled={isLoading}
                  className="px-4 py-2 bg-secondary-blue-light text-secondary-blue border border-secondary-blue/20 rounded-lg text-sm font-inter font-medium hover:bg-secondary-blue/10 hover:border-secondary-blue/30 transition-all duration-200 shadow-soft hover:shadow-blue hover:-translate-y-0.5 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                >
                  <IconComponent className="w-4 h-4 mr-2" />
                  {topic.name}
                </Button>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
}
