import { useState, useRef, useEffect } from "react";
import { Search, BookOpen, Users, Target, Lightbulb } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { usePhilosophySuggestions } from "@/hooks/use-philosophy-graph";
import { SearchSuggestion } from "@/lib/types";

interface SearchInterfaceProps {
  onSearch: (topic: string) => void;
  isLoading: boolean;
}

export function SearchInterface({ onSearch, isLoading }: SearchInterfaceProps) {
  const [searchTopic, setSearchTopic] = useState("");
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [debouncedQuery, setDebouncedQuery] = useState("");
  const inputRef = useRef<HTMLInputElement>(null);
  
  // Debounce search query
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedQuery(searchTopic);
    }, 300);
    
    return () => clearTimeout(timer);
  }, [searchTopic]);

  const { data: suggestions = [] } = usePhilosophySuggestions(debouncedQuery);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchTopic.trim()) {
      onSearch(searchTopic.trim());
      setShowSuggestions(false);
    }
  };

  const handleSuggestionClick = (suggestion: SearchSuggestion) => {
    setSearchTopic(suggestion.name);
    onSearch(suggestion.name);
    setShowSuggestions(false);
  };

  const quickStartTopics = [
    { name: "存在主义", icon: Users },
    { name: "伦理学", icon: Target },
    { name: "意识", icon: Lightbulb },
    { name: "自由意志", icon: BookOpen }
  ];

  return (
    <div className="bg-white border-b border-subtle-grey/20">
      <div className="max-w-4xl mx-auto px-4 py-8">
        <div className="text-center mb-8">
          <h2 className="font-crimson text-3xl font-semibold text-philo-navy mb-2">
            探索哲学概念
          </h2>
          <p className="text-subtle-grey font-source text-lg">
            输入任何哲学主题来生成交互式知识图谱
          </p>
        </div>
        
        <form onSubmit={handleSubmit} className="relative max-w-2xl mx-auto">
          <div className="relative">
            <Input
              ref={inputRef}
              type="text"
              placeholder="输入哲学主题（例如：存在主义、自由意志、德性伦理学）"
              value={searchTopic}
              onChange={(e) => setSearchTopic(e.target.value)}
              onFocus={() => setShowSuggestions(true)}
              onBlur={() => setTimeout(() => setShowSuggestions(false), 200)}
              className="w-full px-6 py-4 text-lg font-source bg-white border-2 border-subtle-grey/30 rounded-xl focus:border-wisdom-purple pr-16"
              disabled={isLoading}
            />
            <Button
              type="submit"
              disabled={!searchTopic.trim() || isLoading}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 bg-wisdom-purple text-white p-3 rounded-lg hover:bg-wisdom-purple/90"
            >
              <Search className="w-4 h-4" />
            </Button>
          </div>
          
          {/* Search Suggestions */}
          {showSuggestions && suggestions.length > 0 && (
            <div className="absolute top-full left-0 right-0 bg-white border border-subtle-grey/20 rounded-lg mt-2 shadow-lg z-10">
              {suggestions.map((suggestion, index) => (
                <div
                  key={index}
                  onClick={() => handleSuggestionClick(suggestion)}
                  className="px-4 py-3 hover:bg-parchment cursor-pointer border-b border-subtle-grey/10 last:border-b-0"
                >
                  <div className="font-medium text-philo-navy">{suggestion.name}</div>
                  <div className="text-sm text-subtle-grey">{suggestion.description}</div>
                </div>
              ))}
            </div>
          )}
        </form>
        
        {/* Quick Start Topics */}
        <div className="mt-6 text-center">
          <p className="text-sm text-subtle-grey mb-3 font-fira">快速开始：</p>
          <div className="flex flex-wrap justify-center gap-2">
            {quickStartTopics.map((topic) => {
              const IconComponent = topic.icon;
              return (
                <Button
                  key={topic.name}
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setSearchTopic(topic.name);
                    onSearch(topic.name);
                  }}
                  disabled={isLoading}
                  className="px-3 py-1 bg-concept-blue/10 text-concept-blue border-concept-blue/20 rounded-full text-sm font-fira hover:bg-concept-blue/20 transition-colors"
                >
                  <IconComponent className="w-3 h-3 mr-1" />
                  {topic.name}
                </Button>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
}
