import { cn } from "@/lib/utils";

interface LoadingSpinnerProps {
  className?: string;
  size?: "sm" | "md" | "lg";
}

export function LoadingSpinner({ className, size = "md" }: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: "w-4 h-4",
    md: "w-8 h-8", 
    lg: "w-12 h-12"
  };

  return (
    <div
      className={cn(
        "border-4 border-subtle-grey/20 border-t-wisdom-purple rounded-full animate-spin",
        sizeClasses[size],
        className
      )}
    />
  );
}
