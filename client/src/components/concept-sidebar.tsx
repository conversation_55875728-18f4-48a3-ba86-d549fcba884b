import { X, Book, Users, Tag } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { GraphNode } from "@/lib/types";

// Simple markdown to HTML converter for basic formatting
function markdownToHtml(text: string): string {
  return text
    // Headers
    .replace(/^## (.*$)/gm, '<h3 class="text-lg font-bold text-philo-navy mb-2 mt-4 first:mt-0">$1</h3>')
    // Bold text
    .replace(/\*\*(.*?)\*\*/g, '<strong class="font-semibold text-philo-navy">$1</strong>')
    // List items
    .replace(/^- \*\*(.*?)\*\*：(.*$)/gm, '<div class="mb-2"><strong class="text-philo-navy">$1</strong>：$2</div>')
    // Line breaks
    .replace(/\n\n/g, '<br><br>')
    .replace(/\n/g, '<br>');
}

interface ConceptSidebarProps {
  node: GraphNode | null;
  isOpen: boolean;
  onClose: () => void;
  onConceptClick: (concept: string) => void;
}

export function ConceptSidebar({ node, isOpen, onClose, onConceptClick }: ConceptSidebarProps) {
  if (!node) return null;

  const categoryColors = {
    core: "bg-concept-blue",
    related: "bg-wisdom-purple",
    principle: "bg-enlighten-orange"
  };

  const categoryIcons = {
    core: Users,
    related: Tag,
    principle: Book
  };

  const IconComponent = categoryIcons[node.category] || Users;

  return (
    <div 
      className={`fixed top-16 right-0 w-96 h-screen bg-white floating-sidebar shadow-xl transform transition-transform duration-300 z-30 ${
        isOpen ? 'translate-x-0' : 'translate-x-full'
      } md:w-96`}
      style={{ width: window.innerWidth < 768 ? '100vw' : '24rem' }}
    >
      <ScrollArea className="h-full">
        <div className="p-6">
          {/* Close Button */}
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="absolute top-4 right-4 p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <X className="w-4 h-4 text-subtle-grey" />
          </Button>
          
          {/* Content Area */}
          <div className="mt-8">
            {/* Header */}
            <div className="mb-6">
              <div className={`w-12 h-12 ${categoryColors[node.category]} rounded-full flex items-center justify-center mb-4`}>
                <IconComponent className="w-6 h-6 text-white" />
              </div>
              <h3 className="font-crimson text-2xl font-semibold text-philo-navy mb-2">
                {node.name}
              </h3>
              <Badge variant="secondary" className="text-subtle-grey font-fira text-sm">
                {node.category === 'core' ? '核心概念' : 
                 node.category === 'related' ? '相关思想' : '关键原理'}
              </Badge>
            </div>
            
            <div className="space-y-6">
              {/* Explanation */}
              <section>
                <h4 className="font-source font-semibold text-philo-navy mb-2">解释</h4>
                <div className="text-subtle-grey font-source text-sm leading-relaxed">
                  <div dangerouslySetInnerHTML={{ __html: markdownToHtml(node.definition) }} />
                </div>
              </section>
              
              {/* Key Philosophers */}
              {node.keyPhilosophers.length > 0 && (
                <section>
                  <h4 className="font-source font-semibold text-philo-navy mb-3 flex items-center">
                    <Users className="w-4 h-4 mr-2" />
                    主要哲学家
                  </h4>
                  <div className="space-y-3">
                    {node.keyPhilosophers.map((philosopher, index) => (
                      <div key={index} className="flex items-start space-x-3 p-3 bg-parchment rounded-lg">
                        <div className={`w-8 h-8 ${categoryColors[node.category]} rounded-full flex items-center justify-center flex-shrink-0`}>
                          <span className="text-white text-xs font-bold">
                            {philosopher.initials}
                          </span>
                        </div>
                        <div>
                          <h5 className="font-medium text-philo-navy">{philosopher.name}</h5>
                          <p className="text-xs text-subtle-grey">{philosopher.description}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </section>
              )}
              
              {/* Key Works */}
              {node.keyWorks.length > 0 && (
                <section>
                  <h4 className="font-source font-semibold text-philo-navy mb-3 flex items-center">
                    <Book className="w-4 h-4 mr-2" />
                    重要著作
                  </h4>
                  <div className="space-y-2">
                    {node.keyWorks.map((work, index) => (
                      <div 
                        key={index}
                        className="flex items-center space-x-2 p-2 hover:bg-parchment rounded transition-colors cursor-pointer"
                      >
                        <Book className={`w-4 h-4 text-${node.category === 'core' ? 'concept-blue' : node.category === 'related' ? 'wisdom-purple' : 'enlighten-orange'}`} />
                        <span className="text-sm text-philo-navy font-source">{work}</span>
                      </div>
                    ))}
                  </div>
                </section>
              )}
              
              {/* Related Concepts */}
              {node.relatedConcepts.length > 0 && (
                <section>
                  <h4 className="font-source font-semibold text-philo-navy mb-3 flex items-center">
                    <Tag className="w-4 h-4 mr-2" />
                    相关概念
                  </h4>
                  <div className="flex flex-wrap gap-2">
                    {node.relatedConcepts.map((concept, index) => (
                      <Badge
                        key={index}
                        variant="outline"
                        className={`px-2 py-1 text-xs font-fira cursor-pointer hover:bg-opacity-20 transition-colors ${
                          node.category === 'core' ? 'text-concept-blue border-concept-blue/20 hover:bg-concept-blue/20' :
                          node.category === 'related' ? 'text-wisdom-purple border-wisdom-purple/20 hover:bg-wisdom-purple/20' :
                          'text-enlighten-orange border-enlighten-orange/20 hover:bg-enlighten-orange/20'
                        }`}
                        onClick={() => onConceptClick(concept)}
                      >
                        {concept}
                      </Badge>
                    ))}
                  </div>
                </section>
              )}
            </div>
          </div>
        </div>
      </ScrollArea>
    </div>
  );
}
