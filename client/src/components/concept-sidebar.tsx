import { X, Book, Users, Tag } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { GraphNode } from "@/lib/types";

// Simple markdown to HTML converter for basic formatting
function markdownToHtml(text: string): string {
  return text
    // Headers
    .replace(/^## (.*$)/gm, '<h3 class="text-lg font-bold text-philo-navy mb-2 mt-4 first:mt-0">$1</h3>')
    // Bold text
    .replace(/\*\*(.*?)\*\*/g, '<strong class="font-semibold text-philo-navy">$1</strong>')
    // List items
    .replace(/^- \*\*(.*?)\*\*：(.*$)/gm, '<div class="mb-2"><strong class="text-philo-navy">$1</strong>：$2</div>')
    // Line breaks
    .replace(/\n\n/g, '<br><br>')
    .replace(/\n/g, '<br>');
}

interface ConceptSidebarProps {
  node: GraphNode | null;
  isOpen: boolean;
  onClose: () => void;
  onConceptClick: (concept: string) => void;
}

export function ConceptSidebar({ node, isOpen, onClose, onConceptClick }: ConceptSidebarProps) {
  if (!node) return null;

  const categoryColors = {
    core: "bg-secondary-blue",
    related: "bg-primary-purple",
    principle: "bg-accent-orange"
  };

  const categoryLightColors = {
    core: "bg-secondary-blue-light",
    related: "bg-primary-purple-light",
    principle: "bg-accent-orange-light"
  };

  const categoryTextColors = {
    core: "text-secondary-blue",
    related: "text-primary-purple",
    principle: "text-accent-orange"
  };

  const categoryIcons = {
    core: Users,
    related: Tag,
    principle: Book
  };

  const IconComponent = categoryIcons[node.category] || Users;

  return (
    <div
      className={`fixed top-16 right-0 w-96 h-screen bg-surface-primary floating-sidebar shadow-large border-l border-gray-200 transform transition-all duration-400 ease-spring z-30 ${
        isOpen ? 'translate-x-0' : 'translate-x-full'
      } md:w-96`}
      style={{ width: window.innerWidth < 768 ? '100vw' : '24rem' }}
    >
      <ScrollArea className="h-full">
        <div className="p-6">
          {/* Close Button */}
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="absolute top-4 right-4 p-2 hover:bg-gray-100 rounded-lg transition-all duration-200 hover:-translate-y-0.5"
          >
            <X className="w-4 h-4 text-gray-500" />
          </Button>

          {/* Content Area */}
          <div className="mt-8 animate-fade-in">
            {/* Header */}
            <div className="mb-8">
              <div className={`w-14 h-14 ${categoryColors[node.category]} rounded-2xl flex items-center justify-center mb-4 shadow-soft`}>
                <IconComponent className="w-7 h-7 text-white" />
              </div>
              <h3 className="font-inter text-2xl font-semibold text-gray-900 mb-3 tracking-tight">
                {node.name}
              </h3>
              <Badge variant="secondary" className={`${categoryLightColors[node.category]} ${categoryTextColors[node.category]} font-inter text-sm font-medium border-0 px-3 py-1`}>
                {node.category === 'core' ? '核心概念' :
                 node.category === 'related' ? '相关思想' : '关键原理'}
              </Badge>
            </div>
            
            <div className="space-y-8">
              {/* Explanation */}
              <section>
                <h4 className="font-inter font-semibold text-gray-900 mb-3 text-sm uppercase tracking-wide">解释</h4>
                <div className="text-gray-700 font-inter text-sm leading-relaxed bg-gray-50 rounded-xl p-4">
                  <div dangerouslySetInnerHTML={{ __html: markdownToHtml(node.definition) }} />
                </div>
              </section>

              {/* Key Philosophers */}
              {node.keyPhilosophers.length > 0 && (
                <section>
                  <h4 className="font-inter font-semibold text-gray-900 mb-4 flex items-center text-sm uppercase tracking-wide">
                    <Users className="w-4 h-4 mr-2" />
                    主要哲学家
                  </h4>
                  <div className="space-y-3">
                    {node.keyPhilosophers.map((philosopher, index) => (
                      <div key={index} className="flex items-start space-x-3 p-4 bg-surface-secondary rounded-xl border border-gray-100 hover:border-gray-200 transition-colors duration-200">
                        <div className={`w-10 h-10 ${categoryColors[node.category]} rounded-xl flex items-center justify-center flex-shrink-0 shadow-soft`}>
                          <span className="text-white text-sm font-semibold">
                            {philosopher.initials}
                          </span>
                        </div>
                        <div className="flex-1">
                          <h5 className="font-inter font-medium text-gray-900 mb-1">{philosopher.name}</h5>
                          <p className="text-sm text-gray-600 leading-relaxed">{philosopher.description}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </section>
              )}
              
              {/* Key Works */}
              {node.keyWorks.length > 0 && (
                <section>
                  <h4 className="font-inter font-semibold text-gray-900 mb-4 flex items-center text-sm uppercase tracking-wide">
                    <Book className="w-4 h-4 mr-2" />
                    重要著作
                  </h4>
                  <div className="space-y-2">
                    {node.keyWorks.map((work, index) => (
                      <div
                        key={index}
                        className="flex items-center space-x-3 p-3 hover:bg-surface-secondary rounded-lg transition-all duration-200 cursor-pointer group"
                      >
                        <Book className={`w-4 h-4 ${categoryTextColors[node.category]} group-hover:scale-110 transition-transform duration-200`} />
                        <span className="text-sm text-gray-900 font-inter group-hover:text-gray-700">{work}</span>
                      </div>
                    ))}
                  </div>
                </section>
              )}

              {/* Related Concepts */}
              {node.relatedConcepts.length > 0 && (
                <section>
                  <h4 className="font-inter font-semibold text-gray-900 mb-4 flex items-center text-sm uppercase tracking-wide">
                    <Tag className="w-4 h-4 mr-2" />
                    相关概念
                  </h4>
                  <div className="flex flex-wrap gap-2">
                    {node.relatedConcepts.map((concept, index) => (
                      <Badge
                        key={index}
                        variant="outline"
                        className={`px-3 py-1.5 text-sm font-inter font-medium cursor-pointer transition-all duration-200 hover:-translate-y-0.5 hover:shadow-soft ${categoryLightColors[node.category]} ${categoryTextColors[node.category]} border-0 hover:scale-105`}
                        onClick={() => onConceptClick(concept)}
                      >
                        {concept}
                      </Badge>
                    ))}
                  </div>
                </section>
              )}
            </div>
          </div>
        </div>
      </ScrollArea>
    </div>
  );
}
