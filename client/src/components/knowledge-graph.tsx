import { useEffect, useRef, useState } from "react";
import * as d3 from "d3";
import { ZoomIn, ZoomOut, Home, RotateCcw } from "lucide-react";
import { Button } from "@/components/ui/button";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { Progress } from "@/components/ui/progress";
import { GraphNode, GraphLink } from "@/lib/types";

interface KnowledgeGraphProps {
  nodes: GraphNode[];
  links: GraphLink[];
  onNodeClick: (node: GraphNode) => void;
  isLoading: boolean;
}

export function KnowledgeGraph({ nodes, links, onNodeClick, isLoading }: KnowledgeGraphProps) {
  const svgRef = useRef<SVGSVGElement>(null);
  const [simulation, setSimulation] = useState<d3.Simulation<GraphNode, GraphLink> | null>(null);
  const [progress, setProgress] = useState(0);
  const [progressText, setProgressText] = useState("");

  const categoryColors = {
    core: "var(--secondary-blue)",
    related: "var(--primary-purple)",
    principle: "var(--accent-orange)"
  };

  // Progress simulation effect
  useEffect(() => {
    if (!isLoading) {
      setProgress(0);
      setProgressText("");
      return;
    }

    const progressSteps = [
      { progress: 15, text: "分析哲学主题..." },
      { progress: 35, text: "识别核心概念..." },
      { progress: 55, text: "构建概念关系..." },
      { progress: 75, text: "生成知识节点..." },
      { progress: 90, text: "优化图谱结构..." },
      { progress: 100, text: "完成生成" }
    ];

    let currentStep = 0;
    const interval = setInterval(() => {
      if (currentStep < progressSteps.length) {
        const step = progressSteps[currentStep];
        setProgress(step.progress);
        setProgressText(step.text);
        currentStep++;
      } else {
        clearInterval(interval);
      }
    }, 1000 + Math.random() * 2000); // Random interval between 1-3 seconds

    return () => clearInterval(interval);
  }, [isLoading]);

  useEffect(() => {
    if (!svgRef.current || nodes.length === 0) return;

    const svg = d3.select(svgRef.current);
    const width = svgRef.current.clientWidth;
    const height = svgRef.current.clientHeight;

    // Clear previous content
    svg.selectAll("*").remove();

    // Setup zoom behavior
    const zoom = d3.zoom<SVGSVGElement, unknown>()
      .scaleExtent([0.1, 4])
      .on("zoom", (event) => {
        container.attr("transform", event.transform);
      });

    svg.call(zoom);

    // Create container for zoomable content
    const container = svg.append("g");

    // Setup simulation
    const sim = d3.forceSimulation<GraphNode>(nodes)
      .force("link", d3.forceLink<GraphNode, GraphLink>(links)
        .id(d => d.id)
        .distance(d => 100 + (1 - d.strength) * 100)
        .strength(d => d.strength * 0.5)
      )
      .force("charge", d3.forceManyBody().strength(-400))
      .force("center", d3.forceCenter(width / 2, height / 2))
      .force("collision", d3.forceCollide().radius(50));

    setSimulation(sim);

    // Create arrowhead marker
    const defs = container.append("defs");
    defs.append("marker")
      .attr("id", "arrowhead")
      .attr("viewBox", "-0 -5 10 10")
      .attr("refX", 25)
      .attr("refY", 0)
      .attr("orient", "auto")
      .attr("markerWidth", 8)
      .attr("markerHeight", 8)
      .attr("xoverflow", "visible")
      .append("svg:path")
      .attr("d", "M 0,-5 L 10 ,0 L 0,5")
      .attr("fill", "var(--subtle-grey)")
      .style("stroke", "none");

    // Create links
    const link = container.append("g")
      .attr("class", "links")
      .selectAll("line")
      .data(links)
      .enter().append("line")
      .attr("stroke", "var(--subtle-grey)")
      .attr("stroke-opacity", 0.6)
      .attr("stroke-width", d => Math.sqrt(d.strength) * 3)
      .attr("marker-end", "url(#arrowhead)");

    // Create nodes
    const node = container.append("g")
      .attr("class", "nodes")
      .selectAll("g")
      .data(nodes)
      .enter().append("g")
      .attr("class", "graph-node")
      .style("cursor", "pointer")
      .call(d3.drag<SVGGElement, GraphNode>()
        .on("start", (event, d) => {
          if (!event.active) sim.alphaTarget(0.3).restart();
          d.fx = d.x;
          d.fy = d.y;
        })
        .on("drag", (event, d) => {
          d.fx = event.x;
          d.fy = event.y;
        })
        .on("end", (event, d) => {
          if (!event.active) sim.alphaTarget(0);
          d.fx = null;
          d.fy = null;
        })
      );

    // Add circles to nodes
    node.append("circle")
      .attr("r", d => {
        switch (d.category) {
          case "core": return 40;
          case "related": return 30;
          case "principle": return 35;
          default: return 25;
        }
      })
      .attr("fill", d => categoryColors[d.category] || categoryColors.related)
      .style("filter", "drop-shadow(2px 2px 4px rgba(0,0,0,0.2))")
      .style("transition", "all 0.3s ease");

    // Add text to nodes
    node.append("text")
      .attr("text-anchor", "middle")
      .attr("dy", ".35em")
      .attr("fill", "white")
      .style("font-family", "Source Sans Pro, sans-serif")
      .style("font-weight", "600")
      .style("font-size", d => {
        const radius = d.category === "core" ? 40 : d.category === "principle" ? 35 : 30;
        return `${Math.max(10, radius / 3)}px`;
      })
      .style("pointer-events", "none")
      .each(function(d) {
        const text = d3.select(this);
        const words = d.name.split(/\s+/);
        const lineHeight = 1.1;
        const maxWidth = (d.category === "core" ? 70 : d.category === "principle" ? 60 : 50);
        
        let line: string[] = [];
        let lineNumber = 0;
        const dy = parseFloat(text.attr("dy"));
        
        text.text(null);
        
        words.forEach(word => {
          line.push(word);
          const testLine = line.join(" ");
          if (testLine.length * 8 > maxWidth && line.length > 1) {
            line.pop();
            text.append("tspan")
              .attr("x", 0)
              .attr("dy", lineNumber === 0 ? dy - 0.5 + "em" : lineHeight + "em")
              .text(line.join(" "));
            line = [word];
            lineNumber++;
          }
        });
        
        if (line.length > 0) {
          text.append("tspan")
            .attr("x", 0)
            .attr("dy", lineNumber === 0 ? dy + "em" : lineHeight + "em")
            .text(line.join(" "));
        }
      });

    // Add hover and click handlers
    node
      .on("mouseenter", function(event, d) {
        // Fix position during hover to prevent jitter
        d.fx = d.x;
        d.fy = d.y;
        
        // Apply hover effect
        d3.select(this).select("circle")
          .transition()
          .duration(150)
          .attr("r", d => {
            const baseRadius = d.category === "core" ? 40 : d.category === "principle" ? 35 : 30;
            return baseRadius * 1.1;
          })
          .style("filter", "drop-shadow(2px 2px 8px rgba(0,0,0,0.3))");
      })
      .on("mouseleave", function(event, d) {
        // Release fixed position
        d.fx = null;
        d.fy = null;
        
        // Remove hover effect
        d3.select(this).select("circle")
          .transition()
          .duration(150)
          .attr("r", d => {
            switch (d.category) {
              case "core": return 40;
              case "related": return 30;
              case "principle": return 35;
              default: return 25;
            }
          })
          .style("filter", "drop-shadow(2px 2px 4px rgba(0,0,0,0.2))");
      })
      .on("click", (event, d) => {
        onNodeClick(d);
      });

    // Update positions on tick
    sim.on("tick", () => {
      link
        .attr("x1", d => (d.source as GraphNode).x!)
        .attr("y1", d => (d.source as GraphNode).y!)
        .attr("x2", d => (d.target as GraphNode).x!)
        .attr("y2", d => (d.target as GraphNode).y!);

      node
        .attr("transform", d => `translate(${d.x},${d.y})`);
    });

    // Store zoom functions on svg element for external access
    (svg.node() as any).zoomIn = () => {
      svg.transition().call(zoom.scaleBy, 1.5);
    };
    
    (svg.node() as any).zoomOut = () => {
      svg.transition().call(zoom.scaleBy, 0.67);
    };
    
    (svg.node() as any).resetView = () => {
      svg.transition().call(zoom.transform, d3.zoomIdentity);
    };

    return () => {
      sim.stop();
    };
  }, [nodes, links, onNodeClick]);

  const handleZoomIn = () => {
    const svg = svgRef.current;
    if (svg && (svg as any).zoomIn) {
      (svg as any).zoomIn();
    }
  };

  const handleZoomOut = () => {
    const svg = svgRef.current;
    if (svg && (svg as any).zoomOut) {
      (svg as any).zoomOut();
    }
  };

  const handleResetView = () => {
    const svg = svgRef.current;
    if (svg && (svg as any).resetView) {
      (svg as any).resetView();
    }
  };

  const handleToggleLayout = () => {
    if (simulation) {
      simulation.alpha(1).restart();
    }
  };

  return (
    <div className="relative flex-1 bg-surface-secondary" style={{ height: "calc(100vh - 240px)" }}>
      {/* Graph Controls */}
      <div className="absolute top-6 left-6 z-20 bg-surface-primary rounded-xl shadow-medium border border-gray-200 p-1 flex items-center space-x-1 backdrop-blur-xl">
        <Button
          variant="ghost"
          size="sm"
          onClick={handleZoomIn}
          className="p-2.5 hover:bg-gray-100 rounded-lg transition-all duration-200 hover:-translate-y-0.5"
          title="放大"
        >
          <ZoomIn className="w-4 h-4 text-gray-700" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={handleZoomOut}
          className="p-2.5 hover:bg-gray-100 rounded-lg transition-all duration-200 hover:-translate-y-0.5"
          title="缩小"
        >
          <ZoomOut className="w-4 h-4 text-gray-700" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={handleResetView}
          className="p-2.5 hover:bg-gray-100 rounded-lg transition-all duration-200 hover:-translate-y-0.5"
          title="重置视图"
        >
          <Home className="w-4 h-4 text-gray-700" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={handleToggleLayout}
          className="p-2.5 hover:bg-gray-100 rounded-lg transition-all duration-200 hover:-translate-y-0.5"
          title="重新布局"
        >
          <RotateCcw className="w-4 h-4 text-gray-700" />
        </Button>
      </div>

      {/* Loading State with Progress */}
      {isLoading && (
        <div className="absolute inset-0 bg-surface-secondary/80 backdrop-blur-xl flex items-center justify-center z-10">
          <div className="bg-surface-primary rounded-2xl shadow-large border border-gray-200 p-8 max-w-md w-full mx-4 animate-scale-in">
            <div className="text-center mb-6">
              <LoadingSpinner size="lg" className="mx-auto mb-4" />
              <h3 className="font-inter text-xl font-semibold text-gray-900 mb-2 tracking-tight">
                正在生成知识图谱
              </h3>
              <p className="text-gray-600 font-inter text-sm leading-relaxed">
                AI正在为您构建哲学概念的知识网络
              </p>
            </div>

            <div className="space-y-4">
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-inter font-medium text-gray-900">
                    {progressText || "准备中..."}
                  </span>
                  <span className="text-sm font-inter text-gray-500 font-mono">
                    {progress}%
                  </span>
                </div>
                <Progress value={progress} className="h-2" />
              </div>

              <div className="text-xs text-gray-500 font-inter text-center">
                这可能需要几秒钟时间，请耐心等待
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Graph SVG */}
      <svg
        ref={svgRef}
        className="w-full h-full"
        style={{ background: "var(--surface-secondary)" }}
      />

      {/* Graph Legend */}
      {nodes.length > 0 && (
        <div className="absolute bottom-6 left-6 bg-surface-primary rounded-xl shadow-medium border border-gray-200 p-4 max-w-xs backdrop-blur-xl animate-fade-in">
          <h4 className="font-inter font-semibold text-gray-900 mb-3 text-sm">图例</h4>
          <div className="space-y-2.5 text-sm font-inter">
            <div className="flex items-center space-x-3">
              <div className="w-3 h-3 bg-secondary-blue rounded-full shadow-soft"></div>
              <span className="text-gray-700">核心概念</span>
            </div>
            <div className="flex items-center space-x-3">
              <div className="w-3 h-3 bg-primary-purple rounded-full shadow-soft"></div>
              <span className="text-gray-700">相关思想</span>
            </div>
            <div className="flex items-center space-x-3">
              <div className="w-3 h-3 bg-accent-orange rounded-full shadow-soft"></div>
              <span className="text-gray-700">关键原理</span>
            </div>
            <div className="flex items-center space-x-3">
              <div className="w-4 h-0.5 bg-gray-400 rounded-full"></div>
              <span className="text-gray-700">关系</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
