export interface PhilosopherInfo {
  name: string;
  description: string;
  initials: string;
}

export interface GraphNode {
  id: string;
  name: string;
  definition: string;
  category: 'core' | 'related' | 'principle';
  keyPhilosophers: PhilosopherInfo[];
  keyWorks: string[];
  relatedConcepts: string[];
  x?: number;
  y?: number;
  fx?: number | null;
  fy?: number | null;
}

export interface GraphLink {
  source: string | GraphNode;
  target: string | GraphNode;
  relationship: string;
  strength: number;
}

export interface PhilosophyGraph {
  topic: string;
  nodes: GraphNode[];
  links: GraphLink[];
}

export interface KnowledgeGraph {
  id: number;
  topic: string;
  nodes: GraphNode[];
  links: GraphLink[];
  createdAt: Date;
}

export interface SearchSuggestion {
  name: string;
  description: string;
}

export interface PhilosophyConcept {
  id: number;
  name: string;
  definition: string;
  category: string;
  keyPhilosophers: PhilosopherInfo[];
  keyWorks: string[];
  relatedConcepts: string[];
  createdAt: Date;
}
