@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Linear-inspired color system */
  --background: hsl(0, 0%, 100%);
  --foreground: hsl(220, 13%, 9%);
  --muted: hsl(220, 13%, 97%);
  --muted-foreground: hsl(220, 9%, 46%);
  --popover: hsl(0, 0%, 100%);
  --popover-foreground: hsl(220, 13%, 9%);
  --card: hsl(0, 0%, 100%);
  --card-foreground: hsl(220, 13%, 9%);
  --border: hsl(220, 13%, 91%);
  --input: hsl(220, 13%, 91%);
  --primary: hsl(262, 83%, 58%);
  --primary-foreground: hsl(0, 0%, 100%);
  --secondary: hsl(220, 13%, 95%);
  --secondary-foreground: hsl(220, 13%, 9%);
  --accent: hsl(220, 13%, 95%);
  --accent-foreground: hsl(220, 13%, 9%);
  --destructive: hsl(0, 84%, 60%);
  --destructive-foreground: hsl(0, 0%, 100%);
  --ring: hsl(262, 83%, 58%);
  --radius: 0.75rem;

  /* Linear-inspired semantic colors */
  --gray-50: hsl(220, 13%, 97%);
  --gray-100: hsl(220, 13%, 95%);
  --gray-200: hsl(220, 13%, 91%);
  --gray-300: hsl(220, 9%, 78%);
  --gray-400: hsl(220, 9%, 60%);
  --gray-500: hsl(220, 9%, 46%);
  --gray-600: hsl(220, 13%, 35%);
  --gray-700: hsl(220, 13%, 26%);
  --gray-800: hsl(220, 13%, 18%);
  --gray-900: hsl(220, 13%, 9%);

  /* Philosophy-specific colors - refined */
  --primary-purple: hsl(262, 83%, 58%);
  --primary-purple-light: hsl(262, 83%, 95%);
  --secondary-blue: hsl(214, 100%, 59%);
  --secondary-blue-light: hsl(214, 100%, 95%);
  --accent-orange: hsl(25, 95%, 53%);
  --accent-orange-light: hsl(25, 95%, 95%);
  --success-green: hsl(142, 76%, 36%);
  --success-green-light: hsl(142, 76%, 95%);

  /* Surface colors */
  --surface-primary: hsl(0, 0%, 100%);
  --surface-secondary: hsl(220, 13%, 98%);
  --surface-tertiary: hsl(220, 13%, 95%);
}

.dark {
  /* Dark mode - Linear inspired */
  --background: hsl(220, 13%, 9%);
  --foreground: hsl(220, 13%, 97%);
  --muted: hsl(220, 13%, 15%);
  --muted-foreground: hsl(220, 9%, 60%);
  --popover: hsl(220, 13%, 12%);
  --popover-foreground: hsl(220, 13%, 97%);
  --card: hsl(220, 13%, 12%);
  --card-foreground: hsl(220, 13%, 97%);
  --border: hsl(220, 13%, 18%);
  --input: hsl(220, 13%, 18%);
  --primary: hsl(262, 83%, 58%);
  --primary-foreground: hsl(0, 0%, 100%);
  --secondary: hsl(220, 13%, 18%);
  --secondary-foreground: hsl(220, 13%, 97%);
  --accent: hsl(220, 13%, 18%);
  --accent-foreground: hsl(220, 13%, 97%);
  --destructive: hsl(0, 84%, 60%);
  --destructive-foreground: hsl(0, 0%, 100%);
  --ring: hsl(262, 83%, 58%);

  /* Dark mode semantic colors */
  --gray-50: hsl(220, 13%, 9%);
  --gray-100: hsl(220, 13%, 12%);
  --gray-200: hsl(220, 13%, 15%);
  --gray-300: hsl(220, 13%, 18%);
  --gray-400: hsl(220, 9%, 35%);
  --gray-500: hsl(220, 9%, 46%);
  --gray-600: hsl(220, 9%, 60%);
  --gray-700: hsl(220, 9%, 78%);
  --gray-800: hsl(220, 13%, 91%);
  --gray-900: hsl(220, 13%, 97%);

  /* Surface colors - dark */
  --surface-primary: hsl(220, 13%, 12%);
  --surface-secondary: hsl(220, 13%, 15%);
  --surface-tertiary: hsl(220, 13%, 18%);
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
    letter-spacing: -0.011em;
  }

  h1, h2, h3, h4, h5, h6 {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    font-weight: 600;
    letter-spacing: -0.025em;
  }

  .font-inter {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  }

  .font-mono {
    font-family: 'JetBrains Mono', 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  }

  /* Improved text rendering */
  .text-balance {
    text-wrap: balance;
  }
}

@layer utilities {
  /* Linear-inspired utility classes */
  .text-gray-50 { color: var(--gray-50); }
  .text-gray-100 { color: var(--gray-100); }
  .text-gray-200 { color: var(--gray-200); }
  .text-gray-300 { color: var(--gray-300); }
  .text-gray-400 { color: var(--gray-400); }
  .text-gray-500 { color: var(--gray-500); }
  .text-gray-600 { color: var(--gray-600); }
  .text-gray-700 { color: var(--gray-700); }
  .text-gray-800 { color: var(--gray-800); }
  .text-gray-900 { color: var(--gray-900); }

  .bg-gray-50 { background-color: var(--gray-50); }
  .bg-gray-100 { background-color: var(--gray-100); }
  .bg-gray-200 { background-color: var(--gray-200); }
  .bg-gray-300 { background-color: var(--gray-300); }
  .bg-gray-400 { background-color: var(--gray-400); }
  .bg-gray-500 { background-color: var(--gray-500); }
  .bg-gray-600 { background-color: var(--gray-600); }
  .bg-gray-700 { background-color: var(--gray-700); }
  .bg-gray-800 { background-color: var(--gray-800); }
  .bg-gray-900 { background-color: var(--gray-900); }

  .border-gray-50 { border-color: var(--gray-50); }
  .border-gray-100 { border-color: var(--gray-100); }
  .border-gray-200 { border-color: var(--gray-200); }
  .border-gray-300 { border-color: var(--gray-300); }
  .border-gray-400 { border-color: var(--gray-400); }
  .border-gray-500 { border-color: var(--gray-500); }
  .border-gray-600 { border-color: var(--gray-600); }
  .border-gray-700 { border-color: var(--gray-700); }
  .border-gray-800 { border-color: var(--gray-800); }
  .border-gray-900 { border-color: var(--gray-900); }

  /* Semantic color utilities */
  .text-primary-purple { color: var(--primary-purple); }
  .text-secondary-blue { color: var(--secondary-blue); }
  .text-accent-orange { color: var(--accent-orange); }
  .text-success-green { color: var(--success-green); }

  .bg-primary-purple { background-color: var(--primary-purple); }
  .bg-primary-purple-light { background-color: var(--primary-purple-light); }
  .bg-secondary-blue { background-color: var(--secondary-blue); }
  .bg-secondary-blue-light { background-color: var(--secondary-blue-light); }
  .bg-accent-orange { background-color: var(--accent-orange); }
  .bg-accent-orange-light { background-color: var(--accent-orange-light); }
  .bg-success-green { background-color: var(--success-green); }
  .bg-success-green-light { background-color: var(--success-green-light); }

  .border-primary-purple { border-color: var(--primary-purple); }
  .border-secondary-blue { border-color: var(--secondary-blue); }
  .border-accent-orange { border-color: var(--accent-orange); }
  .border-success-green { border-color: var(--success-green); }

  /* Surface utilities */
  .bg-surface-primary { background-color: var(--surface-primary); }
  .bg-surface-secondary { background-color: var(--surface-secondary); }
  .bg-surface-tertiary { background-color: var(--surface-tertiary); }
}

/* Linear-inspired component styles */
.graph-node {
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.graph-link {
  stroke-dasharray: 2,3;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.floating-sidebar {
  backdrop-filter: blur(24px) saturate(180%);
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid var(--gray-200);
}

.dark .floating-sidebar {
  background: rgba(0, 0, 0, 0.8);
  border: 1px solid var(--gray-800);
}

/* Linear-inspired animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes shimmer {
  0% { background-position: -200px 0; }
  100% { background-position: calc(200px + 100%) 0; }
}

.animate-fade-in {
  animation: fadeIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-slide-in {
  animation: slideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-scale-in {
  animation: scaleIn 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-shimmer {
  animation: shimmer 2s infinite;
  background: linear-gradient(
    90deg,
    var(--gray-100) 0%,
    var(--gray-50) 50%,
    var(--gray-100) 100%
  );
  background-size: 200px 100%;
}

/* Enhanced button styles */
.btn-primary {
  background: var(--primary-purple);
  color: white;
  border: none;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-primary:hover {
  background: hsl(262, 83%, 52%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

.btn-secondary {
  background: var(--surface-secondary);
  color: var(--gray-700);
  border: 1px solid var(--gray-200);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-secondary:hover {
  background: var(--surface-tertiary);
  border-color: var(--gray-300);
  transform: translateY(-1px);
}

/* Enhanced input styles */
.input-enhanced {
  background: var(--surface-primary);
  border: 1px solid var(--gray-200);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.input-enhanced:focus {
  border-color: var(--primary-purple);
  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
  outline: none;
}

/* Touch-friendly styles for mobile */
@media (max-width: 768px) {
  .graph-node {
    touch-action: manipulation;
  }

  .floating-sidebar {
    width: 100vw !important;
  }

  /* Larger touch targets */
  .btn-mobile {
    min-height: 44px;
    min-width: 44px;
  }
}
