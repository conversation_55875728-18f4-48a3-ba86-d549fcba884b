@import url('https://fonts.googleapis.com/css2?family=Crimson+Text:wght@400;600&family=Source+Sans+Pro:wght@300;400;600&family=Fira+Sans:wght@300;400;500&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: hsl(0, 0%, 100%);
  --foreground: hsl(200, 15%, 8%);
  --muted: hsl(210, 40%, 98%);
  --muted-foreground: hsl(215, 13.8%, 34.1%);
  --popover: hsl(0, 0%, 100%);
  --popover-foreground: hsl(200, 15%, 8%);
  --card: hsl(0, 0%, 100%);
  --card-foreground: hsl(200, 15%, 8%);
  --border: hsl(214.3, 31.8%, 91.4%);
  --input: hsl(214.3, 31.8%, 91.4%);
  --primary: hsl(207, 90%, 54%);
  --primary-foreground: hsl(210, 40%, 98%);
  --secondary: hsl(210, 40%, 98%);
  --secondary-foreground: hsl(222.2, 84%, 4.9%);
  --accent: hsl(210, 40%, 98%);
  --accent-foreground: hsl(222.2, 84%, 4.9%);
  --destructive: hsl(0, 84.2%, 60.2%);
  --destructive-foreground: hsl(210, 40%, 98%);
  --ring: hsl(200, 15%, 8%);
  --radius: 0.5rem;
  
  /* Philosophy-specific colors */
  --philo-navy: hsl(210, 29%, 24%);
  --wisdom-purple: hsl(280, 50%, 40%);
  --enlighten-orange: hsl(22, 82%, 52%);
  --parchment: hsl(210, 17%, 98%);
  --concept-blue: hsl(204, 70%, 53%);
  --subtle-grey: hsl(200, 18%, 46%);
}

.dark {
  --background: hsl(222.2, 84%, 4.9%);
  --foreground: hsl(210, 40%, 98%);
  --muted: hsl(217.2, 32.6%, 17.5%);
  --muted-foreground: hsl(215, 20.2%, 65.1%);
  --popover: hsl(222.2, 84%, 4.9%);
  --popover-foreground: hsl(210, 40%, 98%);
  --card: hsl(222.2, 84%, 4.9%);
  --card-foreground: hsl(210, 40%, 98%);
  --border: hsl(217.2, 32.6%, 17.5%);
  --input: hsl(217.2, 32.6%, 17.5%);
  --primary: hsl(207, 90%, 54%);
  --primary-foreground: hsl(210, 40%, 98%);
  --secondary: hsl(217.2, 32.6%, 17.5%);
  --secondary-foreground: hsl(210, 40%, 98%);
  --accent: hsl(217.2, 32.6%, 17.5%);
  --accent-foreground: hsl(210, 40%, 98%);
  --destructive: hsl(0, 62.8%, 30.6%);
  --destructive-foreground: hsl(210, 40%, 98%);
  --ring: hsl(212.7, 26.8%, 83.9%);
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
    font-family: 'Source Sans Pro', sans-serif;
  }

  h1, h2, h3, h4, h5, h6 {
    font-family: 'Crimson Text', serif;
  }

  .font-crimson {
    font-family: 'Crimson Text', serif;
  }

  .font-source {
    font-family: 'Source Sans Pro', sans-serif;
  }

  .font-fira {
    font-family: 'Fira Sans', sans-serif;
  }
}

@layer utilities {
  .text-philo-navy {
    color: var(--philo-navy);
  }
  
  .text-wisdom-purple {
    color: var(--wisdom-purple);
  }
  
  .text-enlighten-orange {
    color: var(--enlighten-orange);
  }
  
  .text-concept-blue {
    color: var(--concept-blue);
  }
  
  .text-subtle-grey {
    color: var(--subtle-grey);
  }
  
  .bg-philo-navy {
    background-color: var(--philo-navy);
  }
  
  .bg-wisdom-purple {
    background-color: var(--wisdom-purple);
  }
  
  .bg-enlighten-orange {
    background-color: var(--enlighten-orange);
  }
  
  .bg-parchment {
    background-color: var(--parchment);
  }
  
  .bg-concept-blue {
    background-color: var(--concept-blue);
  }
  
  .bg-subtle-grey {
    background-color: var(--subtle-grey);
  }
  
  .border-wisdom-purple {
    border-color: var(--wisdom-purple);
  }
  
  .border-concept-blue {
    border-color: var(--concept-blue);
  }
  
  .border-subtle-grey {
    border-color: var(--subtle-grey);
  }
}

/* Graph-specific styles */
.graph-node {
  cursor: pointer;
}

.graph-link {
  stroke-dasharray: 5,5;
}

.floating-sidebar {
  backdrop-filter: blur(10px);
  background: rgba(248, 249, 250, 0.95);
}

/* Custom animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from { transform: translateX(100%); }
  to { transform: translateX(0); }
}

@keyframes pulseSubtle {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.animate-fade-in {
  animation: fadeIn 0.6s ease-in-out;
}

.animate-slide-in {
  animation: slideIn 0.5s ease-out;
}

.animate-pulse-subtle {
  animation: pulseSubtle 2s infinite;
}

/* Touch-friendly styles for mobile */
@media (max-width: 768px) {
  .graph-node {
    touch-action: manipulation;
  }
  
  .floating-sidebar {
    width: 100vw !important;
  }
}
