import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { KnowledgeGraph, SearchSuggestion, PhilosophyConcept } from "@/lib/types";

export function useGenerateGraph() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (topic: string): Promise<KnowledgeGraph> => {
      const response = await apiRequest("POST", "/api/philosophy/generate", { topic });
      return response.json();
    },
    onSuccess: (data) => {
      // Cache the generated graph
      queryClient.setQueryData(["/api/philosophy/graph", data.topic], data);
    }
  });
}

export function usePhilosophySuggestions(query: string) {
  return useQuery<SearchSuggestion[]>({
    queryKey: ["/api/philosophy/suggestions", { q: query }],
    enabled: query.length > 0,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function usePhilosophyConcept(name: string) {
  return useQuery<PhilosophyConcept>({
    queryKey: ["/api/philosophy/concept", name],
    enabled: !!name,
  });
}

export function useKnowledgeGraph(topic: string) {
  return useQuery<KnowledgeGraph>({
    queryKey: ["/api/philosophy/graph", topic],
    enabled: !!topic,
  });
}

export function usePhilosophyConcepts() {
  return useQuery<PhilosophyConcept[]>({
    queryKey: ["/api/philosophy/concepts"],
  });
}
