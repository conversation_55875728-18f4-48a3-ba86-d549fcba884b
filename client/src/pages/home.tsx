import { useState } from "react";
import { <PERSON>u, Info, Home as HomeIcon } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { SearchInterface } from "@/components/search-interface";
import { KnowledgeGraph } from "@/components/knowledge-graph";
import { ConceptSidebar } from "@/components/concept-sidebar";
import { useGenerateGraph } from "@/hooks/use-philosophy-graph";
import { GraphNode, KnowledgeGraph as KnowledgeGraphType } from "@/lib/types";

export default function Home() {
  const [currentGraph, setCurrentGraph] = useState<KnowledgeGraphType | null>(null);
  const [selectedNode, setSelectedNode] = useState<GraphNode | null>(null);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const { toast } = useToast();

  const generateGraphMutation = useGenerateGraph();

  const handleSearch = async (topic: string) => {
    try {
      const graph = await generateGraphMutation.mutateAsync(topic);
      setCurrentGraph(graph);
      setSelectedNode(null);
      setSidebarOpen(false);
      
      toast({
        title: "知识图谱已生成",
        description: `成功为"${topic}"创建了知识图谱`,
      });
    } catch (error) {
      console.error("Failed to generate graph:", error);
      toast({
        title: "错误",
        description: "生成知识图谱失败，请重试。",
        variant: "destructive",
      });
    }
  };

  const handleNodeClick = (node: GraphNode) => {
    setSelectedNode(node);
    setSidebarOpen(true);
  };

  const handleConceptClick = (conceptName: string) => {
    // Find the concept in current graph nodes
    const node = currentGraph?.nodes.find(n => 
      n.name.toLowerCase() === conceptName.toLowerCase()
    );
    
    if (node) {
      setSelectedNode(node);
    } else {
      // If concept not found in current graph, search for it
      handleSearch(conceptName);
      setSidebarOpen(false);
    }
  };

  const handleCloseSidebar = () => {
    setSidebarOpen(false);
  };

  return (
    <div className="min-h-screen bg-surface-primary">
      {/* Header */}
      <header className="bg-surface-primary/80 backdrop-blur-xl border-b border-gray-200 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-primary-purple rounded-lg flex items-center justify-center shadow-soft">
                <svg
                  className="w-5 h-5 text-white"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  strokeWidth={2.5}
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M13 10V3L4 14h7v7l9-11h-7z"
                  />
                </svg>
              </div>
              <div>
                <h1 className="font-inter text-lg font-semibold text-gray-900 tracking-tight">哲学图谱</h1>
                <p className="text-xs text-gray-500 font-inter">交互式哲学探索器</p>
              </div>
            </div>

            <nav className="hidden md:flex items-center space-x-1">
              <a href="#" className="text-gray-600 hover:text-gray-900 transition-colors font-inter text-sm px-3 py-2 rounded-md hover:bg-gray-100">
                探索
              </a>
              <a href="#" className="text-gray-600 hover:text-gray-900 transition-colors font-inter text-sm px-3 py-2 rounded-md hover:bg-gray-100">
                图书馆
              </a>
              <a href="#" className="text-gray-600 hover:text-gray-900 transition-colors font-inter text-sm px-3 py-2 rounded-md hover:bg-gray-100">
                关于
              </a>
              <Button className="bg-primary-purple text-white px-4 py-2 rounded-lg hover:bg-primary-purple/90 transition-all duration-200 font-inter text-sm font-medium shadow-soft hover:shadow-purple hover:-translate-y-0.5 ml-2">
                登录
              </Button>
            </nav>

            <Button variant="ghost" size="sm" className="md:hidden p-2 hover:bg-gray-100 rounded-md">
              <Menu className="w-5 h-5 text-gray-700" />
            </Button>
          </div>
        </div>
      </header>

      {/* Search Interface */}
      <SearchInterface 
        onSearch={handleSearch} 
        isLoading={generateGraphMutation.isPending}
      />

      {/* Graph Canvas */}
      <KnowledgeGraph
        nodes={currentGraph?.nodes || []}
        links={currentGraph?.links || []}
        onNodeClick={handleNodeClick}
        isLoading={generateGraphMutation.isPending}
      />

      {/* Concept Sidebar */}
      <ConceptSidebar
        node={selectedNode}
        isOpen={sidebarOpen}
        onClose={handleCloseSidebar}
        onConceptClick={handleConceptClick}
      />

      {/* Mobile Controls */}
      <div className="fixed bottom-6 right-6 md:hidden z-20">
        <div className="bg-surface-primary rounded-2xl shadow-large border border-gray-200 p-2 flex items-center space-x-2 backdrop-blur-xl">
          <Button
            size="sm"
            onClick={() => setSidebarOpen(!sidebarOpen)}
            className="p-3 bg-primary-purple text-white rounded-xl hover:bg-primary-purple/90 transition-all duration-200 shadow-soft hover:shadow-purple hover:-translate-y-0.5 btn-mobile"
            disabled={!selectedNode}
          >
            <Info className="w-4 h-4" />
          </Button>
          <Button
            size="sm"
            onClick={() => {
              setCurrentGraph(null);
              setSelectedNode(null);
              setSidebarOpen(false);
            }}
            className="p-3 bg-secondary-blue text-white rounded-xl hover:bg-secondary-blue/90 transition-all duration-200 shadow-soft hover:shadow-blue hover:-translate-y-0.5 btn-mobile"
          >
            <HomeIcon className="w-4 h-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}
